import 'dart:io';
import 'package:amplitude_flutter/amplitude.dart';
import 'package:amplitude_flutter/configuration.dart';
import 'package:amplitude_flutter/events/base_event.dart';

import 'package:gameflex_mobile/config/environment_config.dart';
import 'package:gameflex_mobile/utils/app_logger.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// Service for managing Amplitude analytics initialization and event tracking
/// Handles environment-specific configuration and comprehensive event tracking
class AmplitudeService {
  static final AmplitudeService _instance = AmplitudeService._internal();
  static AmplitudeService get instance => _instance;

  AmplitudeService._internal();

  Amplitude? _amplitude;
  bool _isInitialized = false;
  String? _currentUserId;

  /// Check if Amplitude has been initialized
  bool get isInitialized => _isInitialized;

  /// Get the current user ID
  String? get currentUserId => _currentUserId;

  /// Initialize Amplitude with environment-specific configuration
  Future<void> initialize() async {
    if (_isInitialized) {
      AppLogger.warning('Amplitude already initialized');
      return;
    }

    try {
      const apiKey = "1823fa20cc71d44ef71e7735ed72c3d9";

      _amplitude = Amplitude(Configuration(
        apiKey: apiKey,
        // Enable autocapture features
        defaultTracking: DefaultTrackingOptions(
          sessions: true,        // Track session start/end events
          appLifecycles: true,   // Track app install, update, open, background events
          deepLinks: true,       // Track deep link events (Android only)
        ),
        // Enable remote configuration for dynamic settings
        fetchRemoteConfig: true,
        // Set sample rate for session replay (when available)
        // Note: Session Replay requires separate native plugins for iOS/Android
      ));

      // Wait until the SDK is initialized
      await _amplitude!.isBuilt;

      AppLogger.info(
        'Initializing Amplitude for ${EnvironmentConfig.currentEnvironment} environment',
      );
      AppLogger.info('Platform: ${Platform.isIOS ? 'iOS' : 'Android'}');

      // Set device and app information
      await _setDeviceAndAppInfo();

      _isInitialized = true;
      AppLogger.info('Amplitude initialized successfully');

      // Track app launch
      trackEvent('app_launched', {
        'environment': EnvironmentConfig.currentEnvironment,
        'platform': Platform.isIOS ? 'iOS' : 'Android',
      });
    } catch (e) {
      AppLogger.error('Failed to initialize Amplitude: $e');
    }
  }

  /// Set device and application information for analytics
  Future<void> _setDeviceAndAppInfo() async {
    if (!_isInitialized || _amplitude == null) return;

    try {
      final deviceInfo = DeviceInfoPlugin();

      final properties = <String, dynamic>{
        'app_environment': EnvironmentConfig.currentEnvironment,
        'app_version': '0.0.16', // This should ideally come from package info
      };

      if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        properties.addAll({
          'device_model': iosInfo.model,
          'device_name': iosInfo.name,
          'os_version': iosInfo.systemVersion,
          'platform': 'iOS',
        });
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        properties.addAll({
          'device_model': androidInfo.model,
          'device_brand': androidInfo.brand,
          'os_version': androidInfo.version.release,
          'platform': 'Android',
        });
      }

      // Set user properties using identify
      await setUserProperties(properties);
    } catch (e) {
      AppLogger.warning('Failed to set device info in Amplitude: $e');
    }
  }

  /// Set the user ID for tracking
  Future<void> setUserId(String userId) async {
    if (!_isInitialized || _amplitude == null) {
      AppLogger.warning('Amplitude not initialized, cannot set user ID');
      return;
    }

    try {
      await _amplitude!.setUserId(userId);
      _currentUserId = userId;
      AppLogger.info('Amplitude user ID set: $userId');
    } catch (e) {
      AppLogger.error('Failed to set user ID in Amplitude: $e');
    }
  }

  /// Clear the user ID (for sign out)
  Future<void> clearUserId() async {
    if (!_isInitialized || _amplitude == null) {
      return;
    }

    try {
      await _amplitude!.setUserId(null);
      _currentUserId = null;
      AppLogger.info('Amplitude user ID cleared');
    } catch (e) {
      AppLogger.error('Failed to clear user ID in Amplitude: $e');
    }
  }

  /// Track a custom event with optional properties
  Future<void> trackEvent(
    String eventName, [
    Map<String, dynamic>? properties,
  ]) async {
    if (!_isInitialized || _amplitude == null) {
      AppLogger.warning(
        'Amplitude not initialized, cannot track event: $eventName',
      );
      return;
    }

    try {
      final event = BaseEvent(eventName, eventProperties: properties);
      _amplitude!.track(event);

      AppLogger.info('Amplitude event tracked: $eventName');
    } catch (e) {
      AppLogger.error('Failed to track event in Amplitude: $e');
    }
  }

  /// Set user properties
  Future<void> setUserProperties(Map<String, dynamic> properties) async {
    if (!_isInitialized || _amplitude == null) {
      AppLogger.warning(
        'Amplitude not initialized, cannot set user properties',
      );
      return;
    }

    try {
      // Set user properties one by one using individual events
      // Since Identify might not be available, we'll track individual property events
      for (final entry in properties.entries) {
        await trackEvent('user_property_set', {
          'property_name': entry.key,
          'property_value': entry.value,
        });
      }
      AppLogger.info('Amplitude user properties set');
    } catch (e) {
      AppLogger.error('Failed to set user properties in Amplitude: $e');
    }
  }

  /// Track HTTP requests for network monitoring
  Future<void> recordHttpRequest({
    required String url,
    required String method,
    required int statusCode,
    required int startTime,
    required int endTime,
    int? bytesSent,
    int? bytesReceived,
    String? responseBody,
  }) async {
    final duration = endTime - startTime;

    await trackEvent('http_request', {
      'url': url,
      'method': method,
      'status_code': statusCode,
      'duration_ms': duration,
      'bytes_sent': bytesSent ?? 0,
      'bytes_received': bytesReceived ?? 0,
      'success': statusCode >= 200 && statusCode < 300,
      'error': statusCode >= 400,
      'server_error': statusCode >= 500,
      'client_error': statusCode >= 400 && statusCode < 500,
    });
  }

  /// Track network failures
  Future<void> recordNetworkFailure({
    required String url,
    required String method,
    required int startTime,
    required int endTime,
    required String errorMessage,
    String? errorType,
    int? statusCode,
  }) async {
    final duration = endTime - startTime;

    await trackEvent('network_failure', {
      'url': url,
      'method': method,
      'duration_ms': duration,
      'error_message': errorMessage,
      'error_type': errorType ?? _categorizeError(errorMessage),
      'status_code': statusCode,
    });
  }

  /// Track screen views (replacement for NewRelicNavigationObserver)
  Future<void> recordScreenView(
    String screenName, {
    Map<String, dynamic>? attributes,
  }) async {
    final properties = <String, dynamic>{
      'screen_name': screenName,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    if (attributes != null) {
      properties.addAll(attributes);
    }

    await trackEvent('screen_view', properties);
  }

  /// Record errors (replacement for New Relic error tracking)
  Future<void> recordError(
    dynamic error,
    StackTrace stackTrace, {
    Map<String, dynamic>? attributes,
  }) async {
    final properties = <String, dynamic>{
      'error_message': error.toString(),
      'stack_trace': stackTrace.toString(),
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    if (attributes != null) {
      properties.addAll(attributes);
    }

    await trackEvent('error', properties);
  }

  /// Categorize error types for better analytics
  String _categorizeError(String errorMessage) {
    final message = errorMessage.toLowerCase();

    if (message.contains('timeout') || message.contains('timed out')) {
      return 'timeout';
    } else if (message.contains('connection') || message.contains('network')) {
      return 'connection';
    } else if (message.contains('ssl') || message.contains('certificate')) {
      return 'ssl';
    } else if (message.contains('dns') || message.contains('host')) {
      return 'dns';
    } else if (message.contains('socket')) {
      return 'socket';
    } else {
      return 'unknown';
    }
  }

  /// Flush events (ensure all events are sent)
  Future<void> flush() async {
    if (!_isInitialized || _amplitude == null) {
      return;
    }

    try {
      // Amplitude Flutter doesn't have an explicit flush method
      // Events are automatically batched and sent
      AppLogger.info('Amplitude events flushed');
    } catch (e) {
      AppLogger.error('Failed to flush Amplitude events: $e');
    }
  }
}
